import { Card, CardContent } from '@/components/ui/card';
import { Factory, Users, Award, TrendingUp } from 'lucide-react';

const AboutSection = () => {
  const stats = [
    {
      icon: Factory,
      number: "50+",
      label: "Years of Excellence",
      description: "Serving farmers with quality processing"
    },
    {
      icon: Users,
      number: "10,000+",
      label: "Farmer Network",
      description: "Connected farmers across the region"
    },
    {
      icon: TrendingUp,
      number: "2M+",
      label: "Tons Processed",
      description: "Annual sugarcane processing capacity"
    },
    {
      icon: Award,
      number: "ISO",
      label: "Certified",
      description: "Quality standards and sustainability"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            About Bidri Sugar Factory
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            For over five decades, we've been at the heart of sugarcane farming, 
            supporting farmers with advanced processing capabilities and innovative technology.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center hover:shadow-soft transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="mb-4 flex justify-center">
                  <div className="p-3 bg-gradient-primary rounded-full">
                    <stat.icon className="h-8 w-8 text-hero-fg" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold text-primary mb-2">{stat.number}</h3>
                <h4 className="text-lg font-semibold text-foreground mb-2">{stat.label}</h4>
                <p className="text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-field rounded-2xl p-8 md:p-12 text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-hero-fg mb-6">
            Our Legacy, Your Success
          </h3>
          <p className="text-xl text-hero-fg/90 max-w-4xl mx-auto">
            From traditional farming methods to cutting-edge AI technology, we've evolved 
            to serve our farming community better. Today, SugarBot represents our commitment 
            to bringing modern solutions directly to your fields.
          </p>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;