import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { UserPlus, MessageCircle, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const CTASection = () => {
  return (
    <section className="py-20 bg-gradient-field">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-hero-fg mb-6">
            Join the Smart Farming Revolution
          </h2>
          <p className="text-xl text-hero-fg/90 max-w-3xl mx-auto">
            Take the first step towards smarter, more profitable sugarcane farming. 
            Get started with SugarBot today and see the difference AI can make.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <Card className="bg-card/90 backdrop-blur-sm border-0 hover:shadow-elevated transition-all duration-300 hover:-translate-y-2">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                <div className="p-4 bg-gradient-primary rounded-full w-fit mx-auto">
                  <UserPlus className="h-8 w-8 text-hero-fg" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Register as a Farmer
              </h3>
              <p className="text-muted-foreground mb-6">
                Create your farmer profile and connect with Bidri Sugar Factory's 
                network of agricultural experts and resources.
              </p>
              <Button variant="default" size="lg" className="w-full">
                Register Now
                <ArrowRight className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-card/90 backdrop-blur-sm border-0 hover:shadow-elevated transition-all duration-300 hover:-translate-y-2">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                <div className="p-4 bg-gradient-earth rounded-full w-fit mx-auto">
                  <MessageCircle className="h-8 w-8 text-hero-fg" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Start Chatting with SugarBot
              </h3>
              <p className="text-muted-foreground mb-6">
                Begin your AI-powered farming journey. Upload photos, ask questions, 
                and get expert advice instantly in your local language.
              </p>
              <Link to="/bot" className="w-full block">
                <Button variant="hero" size="lg" className="w-full">
                  Chat with SugarBot
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-12">
          <p className="text-hero-fg/80 text-lg">
            Questions? Contact us at{' '}
            <a href="tel:+918123456789" className="text-earth-gold hover:underline font-semibold">
              +91 81234 56789
            </a>
            {' '}or{' '}
            <a href="mailto:<EMAIL>" className="text-earth-gold hover:underline font-semibold">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default CTASection;