import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  Bug, 
  Calendar, 
  CheckCircle, 
  ExternalLink, 
  Eye, 
  Leaf, 
  Play, 
  Shield, 
  Thermometer, 
  TrendingDown, 
  Zap,
  Youtube,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DiseaseDiagnosisData {
  'Disease Name': string;
  'Youtube video links': string[];
  'Scientific Name': string;
  'Confidence Level': string;
  'Observed Symptoms': string[];
  'Pathogen Type': string;
  'Disease Cycle and Transmission': string;
  'Environmental Triggers': string;
  'Yield Impact': string;
  'Immediate Treatment': string;
  'Preventive Measures': string;
  'Chemical Control': string;
  'Weather Considerations': string;
  'Crop Stage Sensitivity': string;
  'Regional Notes': string;
  'Additional Notes': string;
}

interface DiseaseDiagnosisResponseProps {
  data: DiseaseDiagnosisData;
}

const DiseaseDiagnosisResponse: React.FC<DiseaseDiagnosisResponseProps> = ({ data }) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview', 'symptoms', 'treatment']));
  const [showAllVideos, setShowAllVideos] = useState(false);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const getYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const getYouTubeThumbnail = (videoId: string): string => {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  };

  const openYouTubeVideo = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence.toLowerCase()) {
      case 'high': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPathogenIcon = (pathogenType: string) => {
    switch (pathogenType.toLowerCase()) {
      case 'fungal': return <Bug className="h-4 w-4" />;
      case 'bacterial': return <Zap className="h-4 w-4" />;
      case 'viral': return <AlertTriangle className="h-4 w-4" />;
      default: return <Bug className="h-4 w-4" />;
    }
  };

  // Check if additional information is available
  const hasAdditionalInfo = data['Pathogen Type'] || data['Disease Cycle and Transmission'] ||
    data['Environmental Triggers'] || data['Yield Impact'] || data['Chemical Control'] ||
    data['Weather Considerations'] || data['Crop Stage Sensitivity'] || data['Regional Notes'];

  const basicSections = [
    {
      id: 'overview',
      title: 'Disease Overview',
      icon: <Eye className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">Scientific Name:</span>
                <Badge variant="outline" className="text-xs">
                  {data['Scientific Name']}
                </Badge>
              </div>
              {hasAdditionalInfo && data['Pathogen Type'] && (
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Pathogen Type:</span>
                  <Badge variant="outline" className="text-xs flex items-center gap-1">
                    {getPathogenIcon(data['Pathogen Type'])}
                    {data['Pathogen Type']}
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">Confidence Level:</span>
              <Badge className={cn("text-xs", getConfidenceColor(data['Confidence Level']))}>
                {data['Confidence Level']}
              </Badge>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'symptoms',
      title: 'Observed Symptoms',
      icon: <Leaf className="h-4 w-4" />,
      content: (
        <div className="space-y-2">
          {data['Observed Symptoms'].map((symptom, index) => (
            <div key={index} className="flex items-start gap-2 p-2 bg-orange-50 rounded-lg border border-orange-100">
              <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-orange-800">{symptom}</span>
            </div>
          ))}
        </div>
      )
    },
    {
      id: 'treatment',
      title: 'Immediate Treatment',
      icon: <Shield className="h-4 w-4" />,
      content: (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Zap className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-800 leading-relaxed">
              {data['Immediate Treatment']}
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'prevention',
      title: 'Preventive Measures',
      icon: <CheckCircle className="h-4 w-4" />,
      content: (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="text-sm text-green-800 leading-relaxed whitespace-pre-line">
            {data['Preventive Measures']}
          </div>
        </div>
      )
    }
  ];

  const additionalSections = hasAdditionalInfo ? [
    ...(data['Disease Cycle and Transmission'] ? [{
      id: 'transmission',
      title: 'Disease Cycle & Transmission',
      icon: <TrendingDown className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
          {data['Disease Cycle and Transmission']}
        </div>
      )
    }] : []),
    ...(data['Environmental Triggers'] ? [{
      id: 'environmental',
      title: 'Environmental Triggers',
      icon: <Thermometer className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
          {data['Environmental Triggers']}
        </div>
      )
    }] : []),
    ...(data['Yield Impact'] ? [{
      id: 'impact',
      title: 'Yield Impact',
      icon: <TrendingDown className="h-4 w-4" />,
      content: (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="text-sm text-yellow-800 leading-relaxed whitespace-pre-line">
            {data['Yield Impact']}
          </div>
        </div>
      )
    }] : []),
    ...(data['Chemical Control'] ? [{
      id: 'chemical',
      title: 'Chemical Control',
      icon: <Zap className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
          {data['Chemical Control']}
        </div>
      )
    }] : []),
    ...(data['Weather Considerations'] ? [{
      id: 'weather',
      title: 'Weather Considerations',
      icon: <Calendar className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed">
          {data['Weather Considerations']}
        </div>
      )
    }] : []),
    ...(data['Crop Stage Sensitivity'] ? [{
      id: 'crop-stage',
      title: 'Crop Stage Sensitivity',
      icon: <Calendar className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
          {data['Crop Stage Sensitivity']}
        </div>
      )
    }] : []),
    ...(data['Regional Notes'] ? [{
      id: 'regional',
      title: 'Regional Notes',
      icon: <Eye className="h-4 w-4" />,
      content: (
        <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
          {data['Regional Notes']}
        </div>
      )
    }] : [])
  ] : [];

  const allSections = [...basicSections, ...additionalSections];

  const displayedVideos = showAllVideos ? data['Youtube video links'] : data['Youtube video links'].slice(0, 2);

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card className="border-l-4 border-l-red-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-red-700">
            <AlertTriangle className="h-5 w-5" />
            Disease Diagnosis: {data['Disease Name']}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Disease Information Sections */}
      <div className="space-y-3">
        {allSections.map((section) => (
          <Card key={section.id} className="overflow-hidden">
            <CardHeader
              className="pb-2 cursor-pointer hover:bg-gray-50 transition-colors duration-200"
              onClick={() => toggleSection(section.id)}
            >
              <CardTitle className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  {section.icon}
                  {section.title}
                </div>
                {expandedSections.has(section.id) ? (
                  <ChevronUp className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                )}
              </CardTitle>
            </CardHeader>
            {expandedSections.has(section.id) && (
              <CardContent className="pt-0">
                <Separator className="mb-4" />
                {section.content}
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Additional Notes */}
      {data['Additional Notes'] && (
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-700 mb-2">Additional Notes</h4>
                <p className="text-sm text-blue-800 leading-relaxed">
                  {data['Additional Notes']}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* YouTube Videos Section - Moved to Bottom */}
      {data['Youtube video links'] && data['Youtube video links'].length > 0 && (
        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Youtube className="h-4 w-4 text-red-600" />
              Educational Videos ({data['Youtube video links'].length})
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {displayedVideos.map((videoUrl, index) => {
                const videoId = getYouTubeVideoId(videoUrl);
                return videoId ? (
                  <div
                    key={index}
                    className="relative group cursor-pointer rounded-lg overflow-hidden border border-gray-200 hover:border-red-300 transition-all duration-200 hover:shadow-md"
                    onClick={() => openYouTubeVideo(videoUrl)}
                  >
                    <div className="relative aspect-video">
                      <img
                        src={getYouTubeThumbnail(videoId)}
                        alt={`Video ${index + 1} about ${data['Disease Name']}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to default thumbnail if maxres fails
                          e.currentTarget.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                        }}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                        <div className="bg-red-600 rounded-full p-3 group-hover:scale-110 transition-transform duration-200">
                          <Play className="h-6 w-6 text-white fill-current" />
                        </div>
                      </div>
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-red-600 text-white text-xs">
                          <Youtube className="h-3 w-3 mr-1" />
                          Video {index + 1}
                        </Badge>
                      </div>
                    </div>
                    <div className="p-3 bg-white">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">Click to watch on YouTube</span>
                        <ExternalLink className="h-3 w-3 text-gray-400" />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openYouTubeVideo(videoUrl)}
                      className="w-full"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Watch Video {index + 1}
                    </Button>
                  </div>
                );
              })}
            </div>
            {data['Youtube video links'].length > 2 && (
              <div className="mt-4 text-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAllVideos(!showAllVideos)}
                >
                  {showAllVideos ? (
                    <>
                      <ChevronUp className="h-4 w-4 mr-2" />
                      Show Less
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-2" />
                      Show All {data['Youtube video links'].length} Videos
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DiseaseDiagnosisResponse;
