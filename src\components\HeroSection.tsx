import { But<PERSON> } from '@/components/ui/button';
import { Bot, ChevronDown } from 'lucide-react';
import { Link } from 'react-router-dom';
import heroImage from '@/assets/hero-sugarcane-fields.jpg';

const HeroSection = () => {
  const scrollToBot = () => {
    const botSection = document.getElementById('sugarbot-section');
    botSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section 
      className="relative min-h-screen flex items-center justify-center bg-gradient-hero"
      style={{
        backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${heroImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-gradient-hero opacity-70"></div>
      
      <div className="relative z-10 container mx-auto px-4 text-center">
        <h1 className="text-5xl md:text-7xl font-bold text-hero-fg mb-6 leading-tight">
          Empowering Farmers with
          <span className="block text-earth-gold">Smart Sugarcane Support</span>
        </h1>
        
        <p className="text-xl md:text-2xl text-hero-fg/90 mb-8 max-w-3xl mx-auto">
          Meet SugarBot - Your AI companion for healthier crops, better yields, 
          and smarter farming decisions. Available in your local language.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            variant="hero" 
            size="lg" 
            onClick={scrollToBot}
            className="text-lg px-8 py-4"
          >
            <Bot className="h-5 w-5" />
            Meet SugarBot
          </Button>
          
          <Link to="/bot">
            <Button 
              variant="earth" 
              size="lg" 
              className="text-lg px-8 py-4"
            >
              Start Chatting Now
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ChevronDown className="h-8 w-8 text-hero-fg/70" />
      </div>
    </section>
  );
};

export default HeroSection;