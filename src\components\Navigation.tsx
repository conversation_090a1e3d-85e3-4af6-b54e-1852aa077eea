import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Leaf, Bot } from 'lucide-react';
import GoogleTranslate from "./googleTranslate";

const Navigation = () => {
  const location = useLocation();

  return (
    <nav className="bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <Link to="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
          <Leaf className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold text-foreground">B<PERSON>ri <PERSON></span>
        </Link>
        
        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant={location.pathname === '/' ? 'default' : 'ghost'}>
              Home
            </Button>
          </Link>
          <Link to="/bot">
            <Button variant={location.pathname === '/bot' ? 'hero' : 'ghost'}>
              <Bot className="h-4 w-4" />
              SugarBot
            </Button>
          </Link>
             <div className="flex items-center space-x-3">
            <div className="w-full" style={{ padding: "10px", display: "flex", justifyContent: "flex-end" }}>
          <GoogleTranslate />
        </div>
        </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;