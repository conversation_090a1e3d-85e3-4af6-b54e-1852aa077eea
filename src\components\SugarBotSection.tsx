import { useRef, useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Camera,
  Mic,
  Upload,
  MessageCircle,
  Languages,
  TrendingUp
} from 'lucide-react';
import { Link } from 'react-router-dom';

const SugarBotSection = () => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const [streaming, setStreaming] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        setStreaming(true);
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      alert("Camera access denied or not available.");
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext('2d');
      if (context) {
        context.drawImage(videoRef.current, 0, 0, 300, 225);
        const imageData = canvasRef.current.toDataURL('image/png');
        setCapturedImage(imageData);
      }
    }
  };

  const features = [
    {
      icon: Camera,
      title: "Disease Diagnosis",
      description: (
        <>
          <p className="mb-2">Capture photos of your sugarcane crops and get instant AI-powered disease identification and treatment recommendations.</p>
          <div className="flex flex-col items-center gap-2">
            {!streaming ? (
              <Button variant="secondary" onClick={startCamera}>
                Start Camera
              </Button>
            ) : (
              <>
                <video ref={videoRef} width="300" height="225" className="rounded border" />
                <Button onClick={captureImage}>Capture Image</Button>
                <canvas ref={canvasRef} width="300" height="225" className="hidden" />
              </>
            )}
            {capturedImage && (
              <img src={capturedImage} alt="Captured Crop" className="mt-2 border rounded w-72" />
            )}
          </div>
        </>
      )
    },
    {
      icon: Mic,
      title: "Voice Guidance",
      description:
        "Ask questions in your local language and receive spoken advice about fertilizer application, harvesting, and yield optimization."
    },
    {
      icon: Upload,
      title: "Crop Tracking",
      description:
        "Upload and monitor your crop status throughout the growing season with personalized insights and progress reports."
    },
    {
      icon: Languages,
      title: "Local Language Support",
      description:
        "Communicate in your preferred local language for better understanding and more accurate farming guidance."
    },
    {
      icon: TrendingUp,
      title: "Yield Optimization",
      description:
        "Get data-driven recommendations to maximize your sugarcane yield and improve farm profitability."
    },
    {
      icon: MessageCircle,
      title: "24/7 Support",
      description:
        "Access farming expertise anytime, anywhere. SugarBot is always ready to help with your agricultural questions."
    }
  ];

  return (
    <section id="sugarbot-section" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            How SugarBot Helps You
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our AI-powered assistant combines decades of agricultural expertise with 
            cutting-edge technology to support every aspect of your sugarcane farming.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-elevated transition-all duration-300 hover:-translate-y-2 border-0 bg-card/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="p-3 bg-gradient-primary rounded-full w-fit">
                    <feature.icon className="h-6 w-6 text-hero-fg" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  {feature.title}
                </h3>
                <div className="text-muted-foreground leading-relaxed">
                  {typeof feature.description === 'string' ? (
                    <p>{feature.description}</p>
                  ) : (
                    feature.description
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <div className="bg-gradient-earth rounded-2xl p-8 md:p-12 mb-8">
            <h3 className="text-3xl md:text-4xl font-bold text-hero-fg mb-6">
              Ready to Transform Your Farming?
            </h3>
            <p className="text-xl text-hero-fg/90 mb-8 max-w-2xl mx-auto">
              Join thousands of farmers who are already using SugarBot to improve their 
              sugarcane yields and make smarter farming decisions.
            </p>
            <Link to="/bot">
              <Button variant="hero" size="lg" className="text-lg px-8 py-4">
                Start Using SugarBot
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SugarBotSection;