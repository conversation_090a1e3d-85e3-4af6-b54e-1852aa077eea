import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Star } from 'lucide-react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      location: "Bidar District",
      rating: 5,
      testimonial: "SugarB<PERSON> helped me identify leaf rust early this season. The treatment recommendations saved my entire crop. My yield increased by 30% this year!",
      initials: "R<PERSON>"
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      location: "Kalburgi Region",
      rating: 5,
      testimonial: "The voice feature in Kannada makes it so easy to use. I can ask questions while working in the field and get instant answers about fertilizer timing.",
      initials: "<PERSON>"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      location: "Gulbarga Area",
      rating: 5,
      testimonial: "Amazing technology! The crop tracking feature helps me monitor my 20-acre farm efficiently. SugarBot is like having an agriculture expert in my pocket.",
      initials: "MR"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Stories from Our Farmers
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Real experiences from farmers who are using SugarBot to transform 
            their sugarcane farming practices and achieve better results.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="hover:shadow-soft transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-earth-gold fill-current" />
                  ))}
                </div>
                
                <p className="text-muted-foreground mb-6 leading-relaxed italic">
                  "{testimonial.testimonial}"
                </p>
                
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback className="bg-gradient-primary text-hero-fg font-semibold">
                      {testimonial.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                    <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;