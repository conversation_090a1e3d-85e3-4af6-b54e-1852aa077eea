@tailwind base;
@tailwind components;
@tailwind utilities;

/* Agricultural Design System - Sugarcane & Earth Tones */

@layer base {
  :root {
    --background: 45 15% 96%;
    --foreground: 25 35% 15%;

    --card: 45 25% 98%;
    --card-foreground: 25 35% 15%;

    --popover: 45 25% 98%;
    --popover-foreground: 25 35% 15%;

    --primary: 110 45% 35%;
    --primary-foreground: 45 15% 96%;
    --primary-glow: 110 55% 45%;

    --secondary: 35 40% 65%;
    --secondary-foreground: 25 35% 15%;

    --muted: 45 20% 90%;
    --muted-foreground: 25 25% 45%;

    --accent: 25 50% 60%;
    --accent-foreground: 45 15% 96%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 45 15% 96%;

    --border: 45 20% 85%;
    --input: 45 20% 90%;
    --ring: 110 45% 35%;

    --hero-bg: 110 30% 25%;
    --hero-fg: 45 15% 96%;

    --earth-brown: 25 45% 45%;
    --cane-green: 110 55% 40%;
    --field-green: 95 40% 30%;
    --harvest-gold: 45 75% 65%;

    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-earth: linear-gradient(135deg, hsl(var(--earth-brown)), hsl(var(--harvest-gold)));
    --gradient-field: linear-gradient(180deg, hsl(var(--field-green)), hsl(var(--cane-green)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--hero-bg)), hsl(var(--field-green)));

    --shadow-soft: 0 4px 20px hsl(var(--cane-green) / 0.15);
    --shadow-elevated: 0 8px 30px hsl(var(--primary) / 0.2);

    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.goog-te-gadget-icon {
  display: none !important;
}
