import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Send,
  Mic,
  Stethoscope,
  Heart,
  AlertTriangle,
  Pill,
  Camera,
  X,
  Globe,
} from "lucide-react";
import { useSpeechRecognition } from "react-speech-kit";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ReactTransliterate } from "react-transliterate";
import { cn } from "@/lib/utils";

const languageData = {
  "en-US": { name: "English", nativeName: "English", country: "en-US" },
  "hi-IN": { name: "Hindi", nativeName: "हिन्दी", country: "hi-IN" },
  "mr-IN": { name: "Marathi", nativeName: "मराठी", country: "mr-IN" },
};

const quickHealthActions = [
  {
    title: "Animal Health Diagnosis",
    description: "AI-powered health assessment and diagnosis",
    icon: <Stethoscope className="h-5 w-5" />,
    action: "Analyze the health symptoms of cow ID COW-001: decreased milk production, loss of appetite, and elevated temperature",
  },
  {
    title: "Treatment Recommendations",
    description: "Evidence-based treatment protocols",
    icon: <Pill className="h-5 w-5" />,
    action: "Recommend treatment protocol for buffalo with mastitis symptoms including medication dosage and care instructions",
  },
  {
    title: "Preventive Care Plan",
    description: "Customized prevention strategies",
    icon: <Heart className="h-5 w-5" />,
    action: "Create a preventive care plan for dairy cattle in Satara region to prevent seasonal respiratory infections",
  },
  {
    title: "Emergency Response",
    description: "Urgent health situation guidance",
    icon: <AlertTriangle className="h-5 w-5" />,
    action: "Emergency protocol for suspected foot-and-mouth disease outbreak in dairy cattle - immediate steps required",
  },
];

const DoctorGPT = () => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [isListening, setIsListening] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [language, setLanguage] = useState('en-US');
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const bottomRef = useRef(null);
  const fileInputRef = useRef(null);

  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result) => {
      let cleanResult = '';
      if (typeof result === 'string') {
        cleanResult = result.trim();
      } else if (result && typeof result === 'object') {
        if (result.transcript) {
          cleanResult = String(result.transcript).trim();
        } else if (result.text) {
          cleanResult = String(result.text).trim();
        }
      }

      if (cleanResult && cleanResult !== '[object Object]' && cleanResult.length > 0) {
        setNewMessage(cleanResult);
      }
    },
    onEnd: () => {
      setIsListening(false);
    },
    onError: (error) => {
      console.error("Speech recognition error:", error);
      setIsListening(false);
    },
  });

  useEffect(() => {
    const generateSessionId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };
    const newSessionId = generateSessionId();
    setSessionId(newSessionId);
    setMessages([
      {
        id: 1,
        content: "🩺 **Welcome to DoctorGPT!** \n\nI'm your AI-powered veterinary assistant specialized in **dairy cattle and buffalo healthcare** across Kolhapur district's 12 talukas and 6,000 sansthas. I can help you with:\n\n• **Health Diagnosis** - Analyze symptoms for 234K+ animals\n• **Treatment Plans** - Evidence-based treatment recommendations\n• **Preventive Care** - Customized prevention strategies for cows & buffalos\n• **Emergency Protocols** - Urgent health situation guidance\n• **Medication Dosage** - Safe and effective dosing guidelines\n• **📸 Image Analysis** - Upload photos of animals for visual health assessment\n\nHow can I assist you with dairy animal healthcare in Kolhapur district today?",
        type: "ai",
        time: new Date().toLocaleTimeString(),
      }
    ]);
  }, []);

  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        alert('Image size should be less than 10MB');
        return;
      }
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

const handleSendMessage = async (messageText = null) => {
  let messageToSend = messageText || newMessage;
  if (typeof messageToSend !== 'string') {
    messageToSend = String(messageToSend);
  }
  messageToSend = messageToSend.trim();
  if (!messageToSend && !selectedImage) {
    return;
  }

  const userMessage = {
    id: messages.length + 1,
    content: messageToSend || "📸 Image uploaded for analysis",
    type: "user",
    time: new Date().toLocaleTimeString(),
    image: imagePreview,
  };
  setMessages((prev) => [...prev, userMessage]);
  setNewMessage("");
    removeImage(); // ⬅️ move here to clear image immediately

  setIsListening(false);
  stop();
  setIsTyping(true);

  try {
    let response;
    if (selectedImage) {
      const formData = new FormData();
      formData.append('image', selectedImage);
      if (messageToSend) {
        formData.append('query', messageToSend);
      }
      if (sessionId) {
        formData.append('sessionId', sessionId);
      }
      formData.append('timestamp', Date.now().toString());
      formData.append('language', language.split('-')[0]);
      response = await fetch("https://n8n.onpointsoft.com/webhook/doctor", {
        method: "POST",
        body: formData,
      });
    } else {
      const requestBody = {
        query: messageToSend,
        sessionId,
        timestamp: Date.now(),
        language: language
      };
      response = await fetch("https://n8n.onpointsoft.com/webhook/doctor", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });
    }

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    let responseContent = '';

    if (data.output) {
      responseContent = formatResponseContent(data.output);
    } else if (data.text) {
      responseContent = typeof data.text === 'string' ? data.text : String(data.text);
    } else {
      responseContent = 'No response received';
    }

    const botMessage = {
      id: messages.length + 2,
      content: responseContent,
      type: "ai",
      time: new Date().toLocaleTimeString(),
    };
    setMessages((prev) => [...prev, botMessage]);
    removeImage();
  } catch (err) {
    console.error("Detailed Error:", err);
    setMessages((prev) => [
      ...prev,
      {
        id: messages.length + 2,
        content: `I apologize, but I'm experiencing technical difficulties: ${err.message}. Please try again or contact your veterinarian for immediate assistance.`,
        type: "ai",
        time: new Date().toLocaleTimeString(),
      },
    ]);
  } finally {
    setIsTyping(false);
    // 🧼 Clear image and reset input properly
  removeImage(); // Already there, but move it to finally
  setNewMessage(""); // Ensure input is also cleared
  }
};

const formatResponseContent = (output) => {
  if (typeof output === 'string') {
    return output;
  }

  let formattedContent = '';

  for (const [key, value] of Object.entries(output)) {
    formattedContent += `**${key}:**\n${value}\n\n`;
  }

  return formattedContent;
};


  const handleQuickAction = (actionMessage) => {
    handleSendMessage(actionMessage);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleMic = () => {
    if (!supported) {
      alert("Speech recognition is not supported in your browser. Please use Chrome, Edge, or Safari.");
      return;
    }

    if (listening || isListening) {
      stop();
      setIsListening(false);
    } else {
      setNewMessage("");
      setIsListening(true);
      try {
        listen({
          lang: language,
          continuous: true,
          interimResults: true,
        });
      } catch (error) {
        console.error("Error starting speech recognition:", error);
        setIsListening(false);
        alert("Failed to start speech recognition. Please check your microphone permissions.");
      }
    }
  };

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  return (
    <div className="h-full bg-gradient-to-br from-green-50 to-blue-50 p-6">
      <Card className="w-full h-full flex flex-col shadow-xl border-green-100">
        <CardHeader className="flex-shrink-0 bg-white border-b border-green-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-r from-green-600 to-green-700 p-3 rounded-full">
                <Stethoscope className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">DoctorGPT</h2>
                <p className="text-sm text-green-600">AI Veterinary Healthcare Assistant</p>
                <Badge variant="outline" className="mt-1 bg-green-50 text-green-700 border-green-200">
                  🐄 Specialized for Dairy Animals
                </Badge>
              </div>
            </div>
            
            {/* <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className="text-green-600 hover:bg-green-50"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              >
                <Globe className="h-5 w-5" />
              </Button>
              {showLanguageDropdown && (
                <div className="absolute right-0 mt-2 bg-white border border-green-100 rounded-lg shadow-lg py-1 min-w-[120px] z-50">
                  {Object.entries(languageData).map(([code, lang]) => (
                    <div 
                      key={code} 
                      className="px-3 py-1.5 cursor-pointer hover:bg-green-50 text-sm text-gray-700" 
                      onClick={() => { setLanguage(code); setShowLanguageDropdown(false); }}
                    >
                      {lang.nativeName}
                    </div>
                  ))}
                </div>
              )}
            </div> */}
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden bg-gradient-to-br from-green-50 to-blue-50">
          <ScrollArea className="h-full p-4 space-y-4">
            {messages.length === 1 && (
              <div className="grid grid-cols-2 gap-4 max-w-4xl w-full mb-6">
                {quickHealthActions.map((action, index) => (
                  <Card
                    key={index}
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-green-400 border-green-100 hover:scale-105 transform active:scale-95"
                    onClick={() => handleQuickAction(action.action)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="bg-gradient-to-r from-green-100 to-green-200 p-2 rounded-lg flex-shrink-0 text-green-600">
                          {action.icon}
                        </div>
                        <div className="min-w-0">
                          <h4 className="font-medium text-gray-900 text-sm">
                            {action.title}
                          </h4>
                          <p className="text-xs text-green-600 mt-1">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {messages.map((message) => (
              <div key={message.id} className="mb-3">
    <div className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
      <div className={`${message.type === "user" ? "max-w-xs lg:max-w-md" : "max-w-sm lg:max-w-lg"} p-3 rounded-lg ${
        message.type === "user"
          ? "bg-gradient-to-r from-green-600 to-green-700 text-white shadow-md"
          : "bg-white shadow-md border border-green-100"
      }`}>
                  {message.image && (
                    <div className="mb-2">
                      <img 
                        src={message.image} 
                        alt="Uploaded image" 
                        className="max-w-full h-auto rounded-lg border border-white/20"
                        style={{ maxHeight: '200px' }}
                      />
                    </div>
                  )}
                  <div className="text-sm">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                        ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                        ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                        li: ({ children }) => <li className="mb-1">{children}</li>,
                        h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                        strong: ({ children }) => <strong className="font-bold">{children}</strong>,
                        code: ({ children, className }) => {
                          const isInline = !className;
                          return isInline ? (
                            <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                          ) : (
                            <code className="block bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">{children}</code>
                          );
                        },
                      }}
                    >
                      {typeof message.content === 'string' ? message.content : String(message.content || '')}
                    </ReactMarkdown>
                  </div>
                  {message.time && (
                    <p className="text-xs mt-2 opacity-70">{message.time}</p>
                  )}
                </div>
              </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="max-w-sm lg:max-w-lg p-3 rounded-lg bg-white shadow-md border border-green-100">
                  <div className="flex items-center space-x-2">
                    <div className="animate-pulse text-green-600">
                      🩺 Analyzing your healthcare query...
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={bottomRef} />
          </ScrollArea>
        </CardContent>

        <div className="p-4 bg-white border-t border-green-100 flex-shrink-0">
          {imagePreview && (
            <div className="mb-3 relative inline-block">
              <img 
                src={imagePreview} 
                alt="Preview" 
                className="h-20 w-20 object-cover rounded-lg border border-green-200"
              />
              <Button
                size="sm"
                variant="destructive"
                className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
                onClick={removeImage}
              >
                <X className="h-3 w-3" />
              </Button>
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 rounded-b-lg">
                {selectedImage?.name?.substring(0, 15)}...
              </div>
            </div>
          )}

          <div className="flex space-x-2">
             <div className="relative">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:bg-blue-50"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setShowLanguageDropdown(!showLanguageDropdown);
                            }}
                          >
                            <Globe className="h-5 w-5 mr-1" />
                            <span className="text-sm font-medium">
                              {languageData[language]?.nativeName || "Language"}
                            </span>
                          </Button>
            
                          {showLanguageDropdown && (
                            <div className="absolute left-0 bottom-full mb-2 bg-white border border-blue-100 rounded-lg shadow-lg py-1 min-w-[120px] z-50">
                              {Object.entries(languageData).map(([code, lang]) => (
                                <div
                                  key={code}
                                  className="px-3 py-1.5 cursor-pointer hover:bg-blue-50 text-sm text-gray-700"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    setLanguage(code);
                                    setShowLanguageDropdown(false);
                                  }}
                                >
                                  {lang.nativeName}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
            {language === "en-US" ? (
              <Input
                placeholder={
                  listening || isListening
                    ? "🎤 Listening for healthcare questions..."
                    : "Ask about animal health, symptoms, treatments..."
                }
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                className={cn(
                  "flex-1 border-green-200 focus:border-green-400 focus:ring-green-400",
                  (listening || isListening) &&
                    "border-red-400 ring-4 ring-red-100 bg-red-50"
                )}
              />
            ) : (
              <div className="flex-1">
                <ReactTransliterate
                  lang={language.split("-")[0] as any}
                  value={newMessage}
                  onChangeText={(text) => setNewMessage(text)}
                  onKeyPress={handleKeyPress}
                  placeholder={
                    listening || isListening
                      ? "🎤 आरोग्य प्रश्नांसाठी ऐकत आहे..."
                      : "पशुंच्या आरोग्याबद्दल, लक्षणांबद्दल, उपचारांबद्दल विचारा..."
                  }
                  renderComponent={(props) => (
                    <input
                      {...props}
                      onKeyPress={handleKeyPress}
                      className={cn(
                        "w-full px-3 py-2 border border-green-200 rounded-md focus:border-green-400 focus:ring-green-400 focus:outline-none",
                        (listening || isListening) &&
                          "border-red-400 ring-4 ring-red-100 bg-red-50"
                      )}
                      style={{
                        width: "100%",
                        minWidth: "100%",
                      }}
                    />
                  )}
                />
              </div>
            )}

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageSelect}
              accept="image/*"
              className="hidden"
            />

            <Button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="flex-shrink-0 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white"
              title="Upload image for analysis"
            >
              <Camera className="h-4 w-4" />
            </Button>

            <Button
              type="button"
              onClick={toggleMic}
              disabled={!supported}
              className={cn(
                "flex-shrink-0 text-white relative",
                listening || isListening
                  ? "bg-red-500 hover:bg-red-600 animate-pulse"
                  : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800",
                !supported && "opacity-50 cursor-not-allowed"
              )}
              title={
                !supported
                  ? "Speech recognition not supported"
                  : listening || isListening
                  ? "Click to stop recording"
                  : "Click to start voice input"
              }
            >
              <Mic className={cn(
                "h-4 w-4",
                (listening || isListening) && "animate-bounce"
              )} />
              {(listening || isListening) && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full animate-ping" />
              )}
            </Button>

            <Button
              onClick={() => handleSendMessage()}
              size="icon"
              disabled={!newMessage.trim() && !selectedImage}
              className={cn(
                "flex-shrink-0",
                (newMessage.trim() || selectedImage)
                  ? "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DoctorGPT;
